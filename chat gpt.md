Absolutely — that’s a great idea. In real-life coding projects, especially student ones, work doesn’t happen in neatly boxed weekly chunks. You often bounce between dashboards, revisit old code, or tweak things across weeks.

Here’s your refined, highly realistic logbook with:

 Overlapping dashboard development
 Coding tasks spread naturally (e.g., starting teacher dashboard while still finalizing admin, tweaking student layout later)
 Still aligned with Blackboard's expectations
 Clean Laravel coding logic, student-level terminology (not too advanced)
 Week 11 ends on Thursday — as per today

---

  REALISTIC LOGBOOK (Week 1–Week 11, Coding Overlaps Integrated)

  STUDENT INFO

Name: <PERSON>
Reg No: 668532
Course: APT 3065 VA
Project: Results Management System
Supervisor: Prof. <PERSON><PERSON> O<PERSON>e
Duration: 12 Weeks

---

WEEK 1 – Conceptualization & Topic Approval

Theme: Understanding the problem

| Day   | Activity                                          | Skills Gained     |

| Mon.  | Supervisor briefing and course orientation        | Scope analysis    |
| Tue.  | Researched current school result systems in Kenya | Literature review |
| Wed.  | Identified users: Admin, Teacher, Student         | Role mapping      |
| Thur. | Drafted system objectives and goals               | Problem framing   |
| Fri.  | Sketched core features and possible layout        | Feature planning  |

---

WEEK 2 – Proposal Writing

Theme: Documenting problem and solution path

| Day   | Activity                                             | Skills Gained        |

| Mon.  | Wrote background and problem statement               | Research writing     |
| Tue.  | Added objectives, justification, and expected impact | Proposal development |
| Wed.  | Defined Laravel and MySQL stack in methodology       | Tech alignment       |
| Thur. | Completed and formatted proposal                     | Document polishing   |
| Fri.  | Submitted proposal on Blackboard                     | File submission    |

---

WEEK 3 – System Design

Theme: Structuring logic and database

| Day   | Activity                                                  | Skills Gained       |
| Mon.  | Designed ERD with tables: users, subjects, classes, marks | Schema planning     |
| Tue.  | Created use case diagrams for each user role              | Role-based modeling |
| Wed.  | Started rough dashboard wireframes (Admin/Teacher)        | Interface planning  |
| Thur. | Built simple login form wireframe                         | UI layout           |
| Fri.  | Installed Laravel & configured `.env`, DB, auth setup     | Laravel basics      |

---

WEEK 4 – Progress Report 1

Theme: Design phase review

| Day   | Activity                                   | Skills Gained      |
| Mon.  | Updated wireframes and diagrams            | Visual refinement  |
| Tue.  | Experimented with Laravel Breeze for login | Auth scaffolding   |
| Wed.  | Routed initial login > dashboard flow      | Route testing      |
| Thur. | Google Meet presentation (Team 1/2/3)      | Verbal explanation |
| Fri.  | Began creating Admin model and migration   | Coding kickoff     |

---

WEEK 5 – Admin Dashboard (Started), Teacher Role (Introduced)

Theme: Building backend structure

| Day   | Activity                                        | Skills Gained       |
| Mon.  | Created migrations for users, subjects, classes | Laravel DB setup    |
| Tue.  | Set up AdminController and layout Blade file    | MVC structure       |
| Wed.  | Added admin sidebar with dashboard links        | UI components       |
| Thur. | Started Teacher model and migration             | Multi-role planning |
| Fri.  | Built basic route protection for admin pages    | Middleware usage    |

 Admin and Teacher roles now co-exist — development overlaps

---

WEEK 6 – Teacher Dashboard (Continued), Admin CRUD (Refined)

Theme: Working on both dashboards simultaneously

| Day   | Activity                                             | Skills Gained      |
| Mon.  | Created teacher dashboard layout and route group     | Nested routes      |
| Tue.  | Connected admin CRUD: create/edit/delete users       | Form handling      |
| Wed.  | Made mark entry form (teacher side)                  | Form validation    |
| Thur. | Added subject selector to teacher input form         | Dynamic form logic |
| Fri.  | Fixed class-user relation display on admin dashboard | Relational joins   |

 Alternated between admin polishing and building teacher input screens

---

WEEK 7 – Student Dashboard Begins, Teacher Workflow Continues

Theme: View-side logic and data binding

| Day   | Activity                                      | Skills Gained          |
| Mon.  | Created student role and route guards         | Role middleware        |
| Tue.  | Built student dashboard with Bootstrap cards  | UI building            |
| Wed.  | Populated student dashboard with results data | Eloquent relationships |
| Thur. | Enhanced mark entry checks (teacher side)     | Data logic validation  |
| Fri.  | Improved table layout for viewing marks       | Bootstrap formatting   |

 Teacher and Student roles being coded side-by-side, back-and-forth switching

---

WEEK 8 – Core Integration & Cleanup

Theme: Linking dashboards, cleaning logic

| Day   | Activity                                     | Skills Gained            |
| Mon.  | Connected admin > teacher > student flow     | System flow verification |
| Tue.  | Tweaked mark entry form (edit/update)        | Controller updates       |
| Wed.  | Fixed redirect bug after login               | Redirect handling        |
| Thur. | Added feedback messages with session flashes | UX messaging             |
| Fri.  | Hid links based on roles using `@can`, `@if` | Blade directive logic    |

 Frequent back-and-forth testing between roles — realistic debugging flow

---

WEEK 9 – Blade Cleanup, Input Restrictions

Theme: Refining views, guarding inputs

| Day   | Activity                                               | Skills Gained         |
| Mon.  | Set up default layouts using `@extends` and `@section` | Reusable views        |
| Tue.  | Added numeric validation for marks (0–100)             | Form constraint logic |
| Wed.  | Refined teacher dashboard with subject filter          | Query filtering       |
| Thur. | Disabled edit option for student views                 | UI control            |
| Fri.  | Reviewed overall dashboard logic for consistency       | Manual QA             |

 No new features — focus was view logic and stability

---

WEEK 10 – Navigation, Guards, and Final Touches on UI

Theme: Access control and UX improvements

| Day   | Activity                                                            | Skills Gained      |

| Mon.  | Grouped routes per role using `Route::middleware(['auth', 'role'])` | Route structuring  |
| Tue.  | Set up role-based navbars (admin vs teacher vs student)             | Navbar logic       |
| Wed.  | Cleaned unauthorized access pages                                   | Role protection UX |
| Thur. | Checked layout alignment on small screens                           | Responsiveness     |
| Fri.  | Refactored repeated Blade components into includes                  | Component cleanup  |

---

WEEK 11 – System Fully Working (Code Only)

Theme: Project working — but still under construction (no documentation, no export, no polish)

| Day   | Activity                                              | Skills Gained         |
| Mon.  | Ran full test cycle: login → mark entry → result view | Full cycle validation |
| Tue.  | Added small fixes: duplicate entries, input errors    | Data integrity        |
| Wed.  | Improved sidebar UI using conditional Blade logic     | Clean role-based UI   |
| Thur. | Re-tested flow for different users with sample data   | User simulation       |
| Fri.  | (Pending — ongoing development)                     | —                     |

 Status: Admin, Teacher, Student dashboards all functional
 No documentation, no PDF/Excel export, no backup features yet
 Focus: Core Laravel logic, view security, data correctness


