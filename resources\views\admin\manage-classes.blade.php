@extends('layouts.app')

@section('content')
<h2>Manage Classes</h2>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>All Classes</h5>
            </div>
            <div class="card-body">
                @if($classes->count() > 0)
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Class Name</th>
                                    <th>Students Count</th>
                                    <th>Teachers Count</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($classes as $class)
                                <tr>
                                    <td>{{ $class->name }}</td>
                                    <td>{{ $class->students->count() }}</td>
                                    <td>{{ $class->teachers->count() }}</td>
                                    <td>
                                        <form action="{{ route('admin.delete-class', $class->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure? This will remove all assignments.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No classes created yet.</p>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="{{ route('dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
</div>
@endsection
