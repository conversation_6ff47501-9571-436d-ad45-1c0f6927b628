@extends('layouts.app')

@section('content')
<h2>Teacher Dashboard</h2>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>My Classes</h5>
            </div>
            <div class="card-body">
                @if(auth()->user()->classes->count() > 0)
                    <div class="row">
                        @foreach(auth()->user()->classes as $class)
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $class->name }}</h6>
                                    <p class="card-text">Students: {{ $class->students->count() }}</p>
                                    <a href="{{ route('teacher.enter-marks', $class->id) }}" class="btn btn-primary btn-sm">Enter Marks</a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted">You are not assigned to any classes yet. Please contact the admin.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
