@extends('layouts.app')

@section('content')
<h2>Student Dashboard</h2>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>My Results</h5>
            </div>
            <div class="card-body">
                @if(auth()->user()->marksAsStudent->count() > 0)
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Class</th>
                                    <th>Marks</th>
                                    <th>Exam Type</th>
                                    <th>Teacher</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(auth()->user()->marksAsStudent as $mark)
                                <tr>
                                    <td>{{ $mark->subject->name }}</td>
                                    <td>{{ $mark->class->name }}</td>
                                    <td>
                                        <span class="badge {{ $mark->marks >= 70 ? 'bg-success' : ($mark->marks >= 50 ? 'bg-warning' : 'bg-danger') }}">
                                            {{ $mark->marks }}/{{ $mark->max_marks }}
                                        </span>
                                    </td>
                                    <td>{{ $mark->exam_type }}</td>
                                    <td>{{ $mark->teacher->name }}</td>
                                    <td>{{ $mark->created_at->format('M d, Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No results available yet.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
