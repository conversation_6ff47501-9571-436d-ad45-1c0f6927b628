@extends('layouts.app')

@section('content')
<h2>Edit User</h2>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Edit User: {{ $user->name }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.update-user', $user->id) }}" method="POST">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ $user->name }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ $user->email }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-control" id="role" name="role" required>
                            <option value="admin" {{ $user->role === 'admin' ? 'selected' : '' }}>Admin</option>
                            <option value="teacher" {{ $user->role === 'teacher' ? 'selected' : '' }}>Teacher</option>
                            <option value="student" {{ $user->role === 'student' ? 'selected' : '' }}>Student</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="subjects-section" style="{{ $user->role === 'teacher' ? 'display: block;' : 'display: none;' }}">
                        <label class="form-label">Subjects (for Teachers)</label>
                        @foreach($subjects as $subject)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="subjects[]" value="{{ $subject->id }}" 
                                   id="subject{{ $subject->id }}" {{ $user->subjects->contains($subject->id) ? 'checked' : '' }}>
                            <label class="form-check-label" for="subject{{ $subject->id }}">
                                {{ $subject->name }}
                            </label>
                        </div>
                        @endforeach
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Update User</button>
                    <a href="{{ route('admin.users') }}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('role').addEventListener('change', function() {
    const subjectsSection = document.getElementById('subjects-section');
    if (this.value === 'teacher') {
        subjectsSection.style.display = 'block';
    } else {
        subjectsSection.style.display = 'none';
    }
});
</script>
@endsection
