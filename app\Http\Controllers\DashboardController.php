<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return view('admin.dashboard');
            case 'teacher':
                return view('teacher.dashboard');
            case 'student':
                return view('student.dashboard');
            default:
                return redirect()->route('login');
        }
    }
}
