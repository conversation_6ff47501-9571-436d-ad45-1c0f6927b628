SCHOOL OF INFORMATION AND <PERSON>Y<PERSON>EM TECHNOLOGY.  
STUDENT’S PROJECT LOG-BOOK  
APT/SWE__________________ STUDENT NAME: 
DURATION: 12 WEEKS 
 
 
 
DAILY REPORT  
The daily work carried out during the period of PROJECT  is to be recorded clearly with sketches and diagrams where applicable on the logbook. This must be HANDWRITTEN NOT WEEKLY. 
WEEKLY SUMMARY REPORT  
Take weekly photo or scan and upload on BB logbook section. For the previous weeks use your proposal and design process items to update your weekly items. 
REPORT WRITING  
In addition to the daily and weekly record, the student should submit a report of the work done during the Project. e.g full coverage of the project course, problems encountered e.t.c. A comprehensive guide on report writing is provided by the supervisor on blackboard. 
 
N/B: Note those who don’t submit on Blackboard on time OR wrong uploads are graded 0, late submission 5/10 even after presenting. Ensure you submit on Blackboard and daily manually update  log book report. Ensure you are attending all classes and complying with weekly activities for any grading to be considered. Online Screenshot evidence and physical attendance for SWE will be used to verify student work. 
REPORT SUBMISSION  
The logbook and report must be submitted to the project course supervisor at the end of the Project.  
Attach the letter from the employment that granted you the Project vacancy indicating when the Project started and when it will end.  
The Log-Book should be well bound. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
STUDENT’S PERSONAL INFORMATION
Name of student: Muchai Alvin Mbugua (Surname first)
Registration No. of the student: 668532 Faculty: School of Information and System Technology Course of Study: APT 3065 VA
Stage/year of study: Year 3 Semester 2. Name and Project undertaking: Results Management System - A Laravel-based web application for managing student examination results in Kenyan secondary schools
Name of Project supervisor: Prof. Fredrick Ogore
Mobile: [Your mobile number]
Duration of the Project: 12 Weeks
 
 
 
 
STUDENT’S WEEKLY PROGRESS CHART 
Proposed system (Final system): Results Management System

Proposed System features:
• User authentication with role-based access (Admin, Teacher, Student)
• Digital mark entry and management by teachers
• Automated grade calculations and result processing
• Real-time student result viewing and tracking
• Class and subject management by administrators
• Secure data storage and backup capabilities
APT/SWE Project Log Book Page 4  
WEEK ONE: Project CONCEPTUALIZATION
Theme: Understanding the problem and project scope

DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT
Mon. 	Supervisor briefing and course orientation. Discussed project requirements and expectations	Scope analysis and project planning
Tue. 	Researched current school result systems in Kenya. Studied manual vs digital approaches	Literature review techniques
Wed. 	Identified system users: Admin, Teacher, Student. Analyzed their different needs and roles	Role mapping and user analysis
Thur. 	Drafted system objectives and goals. Defined what the system should achieve	Problem framing and objective setting
Fri. 	Sketched core features and possible layout. Created initial system concept	Feature planning and conceptualization
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..………………… 
 
STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4[Note everything must be handwritten] 
 
WEEK TWO: Project PROPOSAL-Use your objectives Main Objective: 
 
 
 
 
Clear system objectives [Ensure weekly activities match your objective/s] 
•	. 
•	. 
•	. 
•	. 
•	. 
•	. 
 
 
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..………………… 
 
STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  Tools used for UML design: 
 
UML interactions 
 
 
 
 
 
WEEK THREE: Project UML AND SYSTEM DESIGN  
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..………………… STUDENT’S WEEKLY PROGRESS CHART 
WEEK FOUR: Project DESIGN/DATASET/DATABASE 
Describe briefly your design activities 
•	. 
•	. 
•	. 
•	. 
•	. 
•	. 
 
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..………………… STUDENT’S WEEKLY PROGRESS CHART 
 
 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… 
 
STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
WEEK EIGHT: PROGRESS REPORTING#	4 	
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… 
 
STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
WEEK NINE: PROGRESS REPORTING#	5 
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
WEEK TEN: PROGRESS REPORTING#	6 	
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
WEEK ELEVEN: PROGRESS REPORTING#7-REPORT PREPARATION & DOCUMENTATION 
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… 
 
STUDENT’S WEEKLY PROGRESS CHART 
APT/SWE Project Log Book Page 4  
WEEK TWELVE: PROGRESS REPORTING#	8-FINAL PROJECT FINALIZATION 
DAY  	DESCRIPTION OF WEEKLY ACTIVITIES 	NEW SKILLS LEARNT 
Mon. 	  	 
Tue. 	 
 	 
Wed. 	 	 
Thur. 	 	 
Fri. 	 	 
 
 
APT/SWE Project Log Book Page 5  
TRAINEE’S WEEKLY REPORT  
(Please produce a summary for each week of Project) FOR THE USE BY THE INDUSTRIAL SUPERVISOR ONLY  
General comments on the students’ progress  
 
 
NAME OF THE SUPERVISOR………………………………………………………………… 
DEPARTMENT/UNIT 
…………………………...………………………………………………  
DATE………………………………………SIGNATURE…………………..……………… 
 
 
