<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\ClassModel;
use App\Models\Subject;
use App\Models\Mark;
use Illuminate\Support\Facades\Hash;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Admin
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Create Teachers
        $teacher1 = User::create([
            'name' => 'John Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'teacher',
        ]);

        $teacher2 = User::create([
            'name' => 'Mary Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'teacher',
        ]);

        // Create Students
        $student1 = User::create([
            'name' => 'Alice Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
        ]);

        $student2 = User::create([
            'name' => 'Bob Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
        ]);

        // Create Classes
        $class1 = ClassModel::create(['name' => 'Class 2 East']);
        $class2 = ClassModel::create(['name' => 'Class 3 North']);

        // Create Subjects
        $math = Subject::create(['name' => 'Mathematics']);
        $english = Subject::create(['name' => 'English']);
        $science = Subject::create(['name' => 'Science']);
        $kiswahili = Subject::create(['name' => 'Kiswahili']);
        $social = Subject::create(['name' => 'Social Studies']);

        // Assign users to classes
        $teacher1->classes()->attach($class1->id);
        $teacher2->classes()->attach($class2->id);
        $student1->classes()->attach($class1->id);
        $student2->classes()->attach($class1->id);

        // Assign subjects to teachers
        $teacher1->subjects()->attach([$math->id, $english->id, $science->id]);
        $teacher2->subjects()->attach([$kiswahili->id, $social->id]);

        // Create sample marks
        Mark::create([
            'student_id' => $student1->id,
            'teacher_id' => $teacher1->id,
            'class_id' => $class1->id,
            'subject_id' => $math->id,
            'marks' => 85,
            'exam_type' => 'Test',
        ]);

        Mark::create([
            'student_id' => $student1->id,
            'teacher_id' => $teacher1->id,
            'class_id' => $class1->id,
            'subject_id' => $english->id,
            'marks' => 78,
            'exam_type' => 'Midterm',
        ]);
    }
}
