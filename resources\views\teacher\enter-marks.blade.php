@extends('layouts.app')

@section('content')
<h2>Enter Marks - {{ $class->name }}</h2>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Enter New Marks</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('teacher.save-marks') }}" method="POST">
                    @csrf
                    <input type="hidden" name="class_id" value="{{ $class->id }}">

                    <div class="mb-3">
                        <label for="student_id" class="form-label">Select Student</label>
                        <select class="form-control" id="student_id" name="student_id" required>
                            <option value="">Select Student</option>
                            @foreach($students as $student)
                                <option value="{{ $student->id }}">{{ $student->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Select Subject</label>
                        <select class="form-control" id="subject_id" name="subject_id" required>
                            <option value="">Select Subject</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="exam_type" class="form-label">Exam Type</label>
                        <select class="form-control" id="exam_type" name="exam_type" required>
                            <option value="">Select Exam Type</option>
                            <option value="Test">Test</option>
                            <option value="Midterm">Midterm</option>
                            <option value="Final">Final</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="marks" class="form-label">Marks (out of 100)</label>
                        <input type="number" class="form-control" id="marks" name="marks" min="0" max="100" required>
                    </div>

                    <button type="submit" class="btn btn-primary">Save Marks</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Recent Marks</h5>
            </div>
            <div class="card-body">
                @if($marks->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Student</th>
                                    <th>Subject</th>
                                    <th>Marks</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($marks->take(10) as $mark)
                                <tr>
                                    <td>{{ $mark->student->name }}</td>
                                    <td>{{ $mark->subject->name }}</td>
                                    <td>{{ $mark->marks }}/{{ $mark->max_marks }}</td>
                                    <td>{{ $mark->exam_type }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="editMark({{ $mark->id }}, {{ $mark->marks }}, '{{ $mark->exam_type }}')">Edit</button>
                                        <form action="{{ route('teacher.delete-mark', $mark->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No marks entered yet.</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Edit Mark Modal -->
<div class="modal fade" id="editMarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Mark</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editMarkForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_marks" class="form-label">Marks (out of 100)</label>
                        <input type="number" class="form-control" id="edit_marks" name="marks" min="0" max="100" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_exam_type" class="form-label">Exam Type</label>
                        <select class="form-control" id="edit_exam_type" name="exam_type" required>
                            <option value="Test">Test</option>
                            <option value="Midterm">Midterm</option>
                            <option value="Final">Final</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Mark</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="{{ route('dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
</div>

<script>
const updateMarkBaseUrl = "{{ url('teacher/update-mark') }}";

function editMark(markId, marks, examType) {
    document.getElementById('edit_marks').value = marks;
    document.getElementById('edit_exam_type').value = examType;
    document.getElementById('editMarkForm').action = updateMarkBaseUrl + '/' + markId;

    const modal = new bootstrap.Modal(document.getElementById('editMarkModal'));
    modal.show();
}
</script>
@endsection
