<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\StudentController;

// Redirect root to login
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Admin routes
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::post('/register-user', [AdminController::class, 'registerUser'])->name('register-user');
        Route::post('/assign-class', [AdminController::class, 'assignClass'])->name('assign-class');
        Route::post('/create-class', [AdminController::class, 'createClass'])->name('create-class');
        Route::post('/create-subject', [AdminController::class, 'createSubject'])->name('create-subject');

        // User CRUD
        Route::get('/edit-user/{id}', [AdminController::class, 'editUser'])->name('edit-user');
        Route::post('/update-user/{id}', [AdminController::class, 'updateUser'])->name('update-user');
        Route::delete('/delete-user/{id}', [AdminController::class, 'deleteUser'])->name('delete-user');

        // Class management
        Route::get('/manage-classes', [AdminController::class, 'manageClasses'])->name('manage-classes');
        Route::delete('/delete-class/{id}', [AdminController::class, 'deleteClass'])->name('delete-class');

        // Subject management
        Route::get('/manage-subjects', [AdminController::class, 'manageSubjects'])->name('manage-subjects');
        Route::delete('/delete-subject/{id}', [AdminController::class, 'deleteSubject'])->name('delete-subject');
    });

    // Teacher routes
    Route::middleware(['role:teacher'])->prefix('teacher')->name('teacher.')->group(function () {
        Route::get('/classes', [TeacherController::class, 'classes'])->name('classes');
        Route::get('/enter-marks/{class}', [TeacherController::class, 'enterMarks'])->name('enter-marks');
        Route::post('/save-marks', [TeacherController::class, 'saveMarks'])->name('save-marks');
        Route::get('/edit-mark/{id}', [TeacherController::class, 'editMark'])->name('edit-mark');
        Route::post('/update-mark/{id}', [TeacherController::class, 'updateMark'])->name('update-mark');
        Route::delete('/delete-mark/{id}', [TeacherController::class, 'deleteMark'])->name('delete-mark');
    });

    // Student routes
    Route::middleware(['role:student'])->prefix('student')->name('student.')->group(function () {
        Route::get('/results', [StudentController::class, 'results'])->name('results');
    });
});
