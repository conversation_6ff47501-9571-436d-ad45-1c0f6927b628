# Laravel Results Management System - Development Specification

## Project Overview
Build a simple web-based results management system using PHP Laravel to replace paper-based mark recording. The system allows teachers to enter student marks and students to view their results.

## User Roles & Permissions

### 1. Admin
- Register all users (teachers and students)
- Assign teachers to classes
- Assign students to classes
- View all system data
- Manage subjects

### 2. Teacher  
- Enter marks for students in their assigned classes
- View marks for their classes only
- Update marks they've entered
9
### 3. Student
- View their own marks only
- See results by subject/class

## Database Structure (Keep Simple)

### Users Table
```sql
- id
- name
- email  
- password
- role (admin/teacher/student)
- created_at, updated_at
```

### Classes Table
```sql
- id
- name (e.g., "Class 2 East", "Class 3 North")
- created_at, updated_at
```

### Subjects Table
```sql
- id
- name (e.g., "Mathematics", "Science", "English", "Kiswahili", "Social Studies")
- created_at, updated_at
```

### Class_User Table (Pivot)
```sql
- id
- class_id
- user_id
- created_at, updated_at
```

### Marks Table
```sql
- id
- student_id (foreign key to users)
- teacher_id (foreign key to users)
- class_id
- subject_id
- marks (integer)
- max_marks (integer, default 100)
- exam_type (e.g., "Midterm", "Final")
- created_at, updated_at
```

## Required Laravel Components

### Models (with simple relationships)
1. **User.php** - Handle all user types with role column
2. **ClassModel.php** - Manage classes
3. **Subject.php** - Manage subjects  
4. **Mark.php** - Handle mark entries

### Controllers (keep methods simple)
1. **AuthController** - Login/logout
2. **AdminController** - User registration, class assignments
3. **TeacherController** - Enter/edit marks
4. **StudentController** - View own marks
5. **DashboardController** - Role-based home pages

### Views (simple Bootstrap layouts)
1. **Login page**
2. **Admin dashboard** - Forms to add users and assign classes
3. **Teacher dashboard** - Form to enter marks, view class lists
4. **Student dashboard** - Table showing their marks
5. **Simple navigation based on user role**

## Key Features to Implement

### Authentication & Authorization
- Use Laravel's built-in Auth
- Simple middleware to check user roles
- Redirect users to appropriate dashboards after login

### Admin Functions
- **User Registration Form**: Name, Email, Role selection
- **Class Assignment**: Dropdown to select teacher/student and assign to classes
- **Simple lists** to view all users and their assignments

### Teacher Functions  
- **Class Selection**: Dropdown showing only their assigned classes
- **Student List**: Show students in selected class
- **Mark Entry Form**: Simple form with student name, subject, marks input
- **Mark History**: Table showing previously entered marks (editable)

### Student Functions
- **Personal Results Table**: Show all their marks organized by subject
- **Simple filtering** by subject or exam type if needed

## Technical Implementation Notes

### Keep Code Simple
- Use Laravel's Eloquent ORM with basic relationships
- Avoid complex queries - use simple `where()` clauses
- Use Form Requests for validation (basic rules only)
- Implement basic error handling with try-catch

### Database Relationships
```php
// User Model
public function classes() {
    return $this->belongsToMany(ClassModel::class, 'class_user');
}
public function marksAsStudent() {
    return $this->hasMany(Mark::class, 'student_id');
}
public function marksAsTeacher() {
    return $this->hasMany(Mark::class, 'teacher_id');
}

// Mark Model  
public function student() {
    return $this->belongsTo(User::class, 'student_id');
}
public function teacher() {
    return $this->belongsTo(User::class, 'teacher_id');
}
public function subject() {
    return $this->belongsTo(Subject::class);
}
```

### Routing Structure
```php
// Web routes - keep simple
Route::get('/login', [AuthController::class, 'showLogin']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout']);

Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index']);
    
    // Admin routes
    Route::middleware(['role:admin'])->prefix('admin')->group(function () {
        Route::get('/users', [AdminController::class, 'users']);
        Route::post('/register-user', [AdminController::class, 'registerUser']);
        Route::post('/assign-class', [AdminController::class, 'assignClass']);
    });
    
    // Teacher routes
    Route::middleware(['role:teacher'])->prefix('teacher')->group(function () {
        Route::get('/classes', [TeacherController::class, 'classes']);
        Route::get('/enter-marks/{class}', [TeacherController::class, 'enterMarks']);
        Route::post('/save-marks', [TeacherController::class, 'saveMarks']);
    });
    
    // Student routes  
    Route::middleware(['role:student'])->prefix('student')->group(function () {
        Route::get('/results', [StudentController::class, 'results']);
    });
});
```

### Validation Rules (Keep Basic)
```php
// User registration
'name' => 'required|string|max:255',
'email' => 'required|email|unique:users',
'role' => 'required|in:admin,teacher,student'

// Mark entry
'student_id' => 'required|exists:users,id',
'subject_id' => 'required|exists:subjects,id', 
'marks' => 'required|integer|min:0|max:100'
```

## UI/UX Requirements
- Use Bootstrap 5 for simple, clean styling
- Mobile-responsive tables and forms
- Clear navigation showing current user's role
- Simple success/error message display
- Basic form validation feedback

## Security Considerations
- Hash passwords using Laravel's Hash facade
- Validate user permissions before displaying data
- Use CSRF protection on all forms
- Sanitize all inputs

## Deployment Notes
- Use SQLite for development (simple setup)
- Include database seeders for demo data
- Clear documentation for setup steps
- Simple .env configuration

## Success Criteria
The system should allow:
1. Admin to quickly register users and assign classes
2. Teachers to easily enter student marks
3. Students to immediately view their results
4. All operations without paper records
5. Code that's easy to explain and understand

## Development Priority
1. Authentication system
2. User management (Admin functions)
3. Class assignment functionality
4. Mark entry system (Teacher functions)
5. Results viewing (Student functions)
6. Basic UI improvements

This specification prioritizes simplicity and functionality over advanced features, making it perfect for a school project that needs to be easily explained and demonstrated.