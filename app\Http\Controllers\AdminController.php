<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ClassModel;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function users()
    {
        $users = User::all();
        $classes = ClassModel::all();
        $subjects = Subject::all();
        return view('admin.users', compact('users', 'classes', 'subjects'));
    }

    public function registerUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'role' => 'required|in:admin,teacher,student',
            'password' => 'required|min:6',
            'subjects' => 'array',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => Hash::make($request->password),
        ]);

        // If teacher, assign subjects
        if ($request->role === 'teacher' && $request->subjects) {
            $user->subjects()->attach($request->subjects);
        }

        return back()->with('success', 'User registered successfully!');
    }

    public function assignClass(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
        ]);

        $user = User::find($request->user_id);
        $user->classes()->attach($request->class_id);

        return back()->with('success', 'User assigned to class successfully!');
    }

    public function createClass(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        ClassModel::create(['name' => $request->name]);

        return back()->with('success', 'Class created successfully!');
    }

    public function createSubject(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        Subject::create(['name' => $request->name]);

        return back()->with('success', 'Subject created successfully!');
    }

    // User CRUD
    public function editUser($id)
    {
        $user = User::findOrFail($id);
        $subjects = Subject::all();
        return view('admin.edit-user', compact('user', 'subjects'));
    }

    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'role' => 'required|in:admin,teacher,student',
            'subjects' => 'array',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ]);

        // Update teacher subjects
        if ($request->role === 'teacher') {
            $user->subjects()->sync($request->subjects ?? []);
        } else {
            $user->subjects()->detach();
        }

        return redirect()->route('admin.users')->with('success', 'User updated successfully!');
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return back()->with('success', 'User deleted successfully!');
    }

    // Class management
    public function manageClasses()
    {
        $classes = ClassModel::all();
        return view('admin.manage-classes', compact('classes'));
    }

    public function deleteClass($id)
    {
        $class = ClassModel::findOrFail($id);
        $class->delete();
        return back()->with('success', 'Class deleted successfully!');
    }

    // Subject management
    public function manageSubjects()
    {
        $subjects = Subject::all();
        return view('admin.manage-subjects', compact('subjects'));
    }

    public function deleteSubject($id)
    {
        $subject = Subject::findOrFail($id);
        $subject->delete();
        return back()->with('success', 'Subject deleted successfully!');
    }
}
