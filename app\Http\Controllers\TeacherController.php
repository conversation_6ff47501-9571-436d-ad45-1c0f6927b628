<?php

namespace App\Http\Controllers;

use App\Models\ClassModel;
use App\Models\Subject;
use App\Models\Mark;
use Illuminate\Http\Request;

class TeacherController extends Controller
{
    public function classes()
    {
        $teacher = auth()->user();
        $classes = $teacher->classes;
        return view('teacher.classes', compact('classes'));
    }

    public function enterMarks($classId)
    {
        $teacher = auth()->user();
        $class = ClassModel::findOrFail($classId);

        // Check if teacher is assigned to this class
        if (!$teacher->classes->contains($classId)) {
            abort(403, 'Unauthorized');
        }

        $students = $class->students;
        // Only show subjects that teacher teaches
        $subjects = $teacher->subjects;
        $marks = Mark::where('class_id', $classId)
                    ->where('teacher_id', $teacher->id)
                    ->with(['student', 'subject'])
                    ->get();

        return view('teacher.enter-marks', compact('class', 'students', 'subjects', 'marks'));
    }

    public function saveMarks(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:users,id',
            'subject_id' => 'required|exists:subjects,id',
            'class_id' => 'required|exists:classes,id',
            'marks' => 'required|integer|min:0|max:100',
            'exam_type' => 'required|string',
        ]);

        Mark::create([
            'student_id' => $request->student_id,
            'teacher_id' => auth()->id(),
            'class_id' => $request->class_id,
            'subject_id' => $request->subject_id,
            'marks' => $request->marks,
            'exam_type' => $request->exam_type,
        ]);

        return back()->with('success', 'Marks saved successfully!');
    }

    public function editMark($id)
    {
        $mark = Mark::findOrFail($id);

        // Check if teacher owns this mark
        if ($mark->teacher_id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        return response()->json($mark);
    }

    public function updateMark(Request $request, $id)
    {
        $mark = Mark::findOrFail($id);

        // Check if teacher owns this mark
        if ($mark->teacher_id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'marks' => 'required|integer|min:0|max:100',
            'exam_type' => 'required|string',
        ]);

        $mark->update([
            'marks' => $request->marks,
            'exam_type' => $request->exam_type,
        ]);

        return back()->with('success', 'Marks updated successfully!');
    }

    public function deleteMark($id)
    {
        $mark = Mark::findOrFail($id);

        // Check if teacher owns this mark
        if ($mark->teacher_id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        $mark->delete();
        return back()->with('success', 'Marks deleted successfully!');
    }
}
